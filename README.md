# Elastic Email CSV Extractor

This script extracts hard bounce emails and unsubscribes from multiple Elastic Email CSV exports.

## Features

- Processes multiple CSV files in specified directories
- Automatically detects email columns in CSV files
- Filters emails based on status (hard bounces vs unsubscribes)
- Removes duplicates and outputs clean email lists
- Handles various CSV formats and encodings
- Generates timestamped output files

## Quick Start

### Option 1: Run with Batch File (Windows)
1. Double-click `run_email_extractor.bat`
2. The script will automatically process the default directories:
   - Hard bounces: `H:\Master Bounces and Unsubs\Bounces\hardbounces`
   - Unsubscribes: `H:\Master Bounces and Unsubs\Bounces\unsubscribed`

### Option 2: Run with Python Command Line

```bash
# Install dependencies
pip install -r requirements.txt

# Run with default directories
python elastic_email_extractor.py

# Run with custom directories
python elastic_email_extractor.py --hardbounces-dir "C:\path\to\hardbounces" --unsubscribes-dir "C:\path\to\unsubscribes" --output-dir "C:\output"
```

## Command Line Options

- `--hardbounces-dir`: Directory containing hard bounce CSV files (default: `H:\Master Bounces and Unsubs\Bounces\hardbounces`)
- `--unsubscribes-dir`: Directory containing unsubscribe CSV files (default: `H:\Master Bounces and Unsubs\Bounces\unsubscribed`)
- `--output-dir`: Directory to save extracted email lists (default: current directory)

## Output Files

The script generates two CSV files with timestamps:
- `hardbounce_emails_YYYYMMDD_HHMMSS.csv` - Contains all unique hard bounce emails
- `unsubscribe_emails_YYYYMMDD_HHMMSS.csv` - Contains all unique unsubscribe emails

## CSV Format Requirements

The script automatically detects:
- Email columns (looks for columns containing 'email', 'mail', or 'address')
- Status columns (for filtering hard bounces and unsubscribes)
- Various CSV delimiters and encodings

## Supported Elastic Email Export Formats

The script works with standard Elastic Email CSV exports and will attempt to:
1. Find email addresses in any column that looks like it contains emails
2. Filter by status if available (looking for 'bounce' or 'unsubscrib' in status columns)
3. Handle various CSV formats and encodings automatically

## Troubleshooting

1. **"Directory does not exist" error**: Make sure the paths to your CSV directories are correct
2. **"No CSV files found" error**: Ensure your directories contain .csv files
3. **Python not found**: Install Python from python.org and ensure it's in your PATH
4. **Permission errors**: Run as administrator if accessing network drives

## Requirements

- Python 3.6 or higher
- pandas library (automatically installed by the batch script)
